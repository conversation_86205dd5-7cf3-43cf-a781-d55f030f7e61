/**
 * Utility functions for WebSocket connection handling
 */

export interface WebSocketStatus {
  connected: boolean;
  connecting?: boolean;
  error?: string | null;
  reconnectAttempt?: number;
}

/**
 * Waits for WebSocket connection to be established with a timeout
 * @param wsStatus - Current WebSocket status
 * @param maxWaitTime - Maximum time to wait in milliseconds (default: 5000)
 * @returns Promise<boolean> - true if connected, false if timeout or error
 */
export const waitForWebSocketConnection = async (
  wsStatus: WebSocketStatus,
  maxWaitTime: number = 5000
): Promise<boolean> => {
  const startTime = Date.now();
  
  while (Date.now() - startTime < maxWaitTime) {
    if (wsStatus.connected) {
      return true;
    }
    
    // If there's a connection error, don't wait
    if (wsStatus.error) {
      return false;
    }
    
    // Wait 100ms before checking again
    await new Promise(resolve => setTimeout(resolve, 100));
  }
  
  return false;
};

/**
 * Creates a connection error toast configuration
 */
export const createConnectionErrorToast = () => ({
  title: 'Connection Error',
  description: 'Chat service is not available. Please try again later.',
  variant: 'destructive' as const,
});
